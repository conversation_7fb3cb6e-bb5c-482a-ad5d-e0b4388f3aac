#!/usr/bin/env python3
"""
DEMO WITHOUT OLLAMA
Demonstrates the AI trading system capabilities without requiring Ollama
"""

import os
import sys
import time
import json
import random
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any

class MockAIAgent:
    """Mock AI agent that simulates AI responses"""
    
    def __init__(self, name: str, specialization: str):
        self.name = name
        self.specialization = specialization
        self.response_time = random.uniform(1.0, 3.0)
        
    def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """Generate mock AI analysis"""
        
        # Simulate thinking time
        time.sleep(self.response_time)
        
        # Generate realistic-looking analysis
        actions = ['BUY', 'SELL', 'HOLD']
        weights = [0.3, 0.2, 0.5]  # Favor HOLD for safety
        
        action = random.choices(actions, weights=weights)[0]
        confidence = random.uniform(0.4, 0.9)
        
        # Generate reasoning based on specialization
        reasoning_templates = {
            'technical_analysis': f"Technical indicators for {symbol} show {action.lower()} signal with RSI at {random.randint(30, 70)} and moving averages suggesting {action.lower()} momentum.",
            'fundamental_analysis': f"Fundamental analysis of {symbol} indicates {action.lower()} opportunity based on P/E ratio of {random.uniform(15, 25):.1f} and strong earnings growth.",
            'risk_assessment': f"Risk analysis for {symbol} suggests {action.lower()} position with volatility at {random.uniform(15, 35):.1f}% and correlation risk moderate.",
            'momentum_trading': f"Momentum indicators for {symbol} show {action.lower()} signal with price momentum and volume confirming the trend.",
            'market_sentiment': f"Market sentiment analysis for {symbol} indicates {action.lower()} bias with social sentiment score of {random.uniform(0.3, 0.8):.2f}."
        }
        
        reasoning = reasoning_templates.get(self.specialization, f"Analysis suggests {action} for {symbol}")
        
        return {
            'symbol': symbol,
            'action': action,
            'confidence': confidence,
            'reasoning': reasoning,
            'agent': self.name,
            'specialization': self.specialization,
            'response_time': self.response_time,
            'timestamp': datetime.now().isoformat()
        }

class MockTradingSystem:
    """Mock trading system for demonstration"""
    
    def __init__(self):
        self.initial_capital = 100000.0
        self.current_cash = self.initial_capital
        self.positions = {}
        self.trades = []
        self.portfolio_history = []
        
        # Mock AI team
        self.ai_team = {
            'technical_analyst': MockAIAgent('TechnicalAnalyst', 'technical_analysis'),
            'fundamental_analyst': MockAIAgent('FundamentalAnalyst', 'fundamental_analysis'),
            'risk_manager': MockAIAgent('RiskManager', 'risk_assessment'),
            'momentum_trader': MockAIAgent('MomentumTrader', 'momentum_trading'),
            'sentiment_analyzer': MockAIAgent('SentimentAnalyzer', 'market_sentiment')
        }
        
        # Mock market data
        self.market_prices = {
            'AAPL': 150.0,
            'MSFT': 300.0,
            'GOOGL': 2500.0,
            'AMZN': 3000.0,
            'TSLA': 200.0,
            'NVDA': 800.0,
            'META': 250.0,
            'NFLX': 400.0
        }
        
        print("🚀 MOCK AI TRADING SYSTEM INITIALIZED")
        print(f"💰 Initial Capital: ${self.initial_capital:,.2f}")
        print(f"🤖 AI Team: {len(self.ai_team)} agents")
        print(f"📊 Trading Symbols: {list(self.market_prices.keys())}")
        
    def get_market_price(self, symbol: str) -> float:
        """Get current market price (with random fluctuation)"""
        base_price = self.market_prices.get(symbol, 100.0)
        # Add random fluctuation ±2%
        fluctuation = random.uniform(-0.02, 0.02)
        return base_price * (1 + fluctuation)
        
    def get_ai_consensus(self, symbol: str) -> Dict[str, Any]:
        """Get AI team consensus for a symbol"""
        
        print(f"\n🤖 Getting AI consensus for {symbol}...")
        
        analyses = []
        
        # Get analysis from each AI agent
        for agent_name, agent in self.ai_team.items():
            print(f"  🔍 {agent_name} analyzing...")
            analysis = agent.analyze_symbol(symbol)
            analyses.append(analysis)
            print(f"    {analysis['action']} (confidence: {analysis['confidence']:.2f})")
        
        # Calculate consensus
        buy_votes = sum(1 for a in analyses if a['action'] == 'BUY')
        sell_votes = sum(1 for a in analyses if a['action'] == 'SELL')
        hold_votes = sum(1 for a in analyses if a['action'] == 'HOLD')
        
        total_votes = len(analyses)
        avg_confidence = sum(a['confidence'] for a in analyses) / total_votes
        
        # Determine consensus action
        if buy_votes > sell_votes and buy_votes > hold_votes:
            consensus_action = 'BUY'
        elif sell_votes > buy_votes and sell_votes > hold_votes:
            consensus_action = 'SELL'
        else:
            consensus_action = 'HOLD'
            
        consensus_strength = max(buy_votes, sell_votes, hold_votes) / total_votes
        
        return {
            'symbol': symbol,
            'consensus_action': consensus_action,
            'consensus_strength': consensus_strength,
            'average_confidence': avg_confidence,
            'vote_breakdown': {
                'BUY': buy_votes,
                'SELL': sell_votes,
                'HOLD': hold_votes
            },
            'individual_analyses': analyses,
            'timestamp': datetime.now().isoformat()
        }
        
    def execute_trade(self, symbol: str, action: str, quantity: int, reasoning: str) -> bool:
        """Execute a trade"""
        
        current_price = self.get_market_price(symbol)
        trade_value = quantity * current_price
        commission = trade_value * 0.001  # 0.1% commission
        
        if action == 'BUY':
            total_cost = trade_value + commission
            if self.current_cash >= total_cost:
                self.current_cash -= total_cost
                
                if symbol in self.positions:
                    # Add to existing position
                    old_qty = self.positions[symbol]['quantity']
                    old_avg = self.positions[symbol]['avg_price']
                    new_qty = old_qty + quantity
                    new_avg = ((old_qty * old_avg) + trade_value) / new_qty
                    
                    self.positions[symbol] = {
                        'quantity': new_qty,
                        'avg_price': new_avg,
                        'current_price': current_price
                    }
                else:
                    # New position
                    self.positions[symbol] = {
                        'quantity': quantity,
                        'avg_price': current_price,
                        'current_price': current_price
                    }
                
                self.trades.append({
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': current_price,
                    'value': trade_value,
                    'commission': commission,
                    'reasoning': reasoning
                })
                
                return True
            else:
                print(f"  ❌ Insufficient cash for {symbol} purchase")
                return False
                
        elif action == 'SELL':
            if symbol in self.positions and self.positions[symbol]['quantity'] >= quantity:
                self.current_cash += trade_value - commission
                
                self.positions[symbol]['quantity'] -= quantity
                if self.positions[symbol]['quantity'] == 0:
                    del self.positions[symbol]
                
                self.trades.append({
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': current_price,
                    'value': trade_value,
                    'commission': commission,
                    'reasoning': reasoning
                })
                
                return True
            else:
                print(f"  ❌ Insufficient position in {symbol} for sale")
                return False
                
        return False
        
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value"""
        positions_value = 0
        
        for symbol, position in self.positions.items():
            current_price = self.get_market_price(symbol)
            position['current_price'] = current_price
            positions_value += position['quantity'] * current_price
            
        return self.current_cash + positions_value
        
    def print_portfolio_summary(self):
        """Print portfolio summary"""
        
        total_value = self.get_portfolio_value()
        positions_value = total_value - self.current_cash
        total_pnl = total_value - self.initial_capital
        pnl_percent = (total_pnl / self.initial_capital) * 100
        
        print(f"\n📊 PORTFOLIO SUMMARY")
        print(f"💰 Cash: ${self.current_cash:,.2f}")
        print(f"📈 Positions Value: ${positions_value:,.2f}")
        print(f"💎 Total Value: ${total_value:,.2f}")
        print(f"📊 P&L: ${total_pnl:,.2f} ({pnl_percent:+.2f}%)")
        print(f"🔢 Total Trades: {len(self.trades)}")
        
        if self.positions:
            print(f"\n📋 Current Positions:")
            for symbol, position in self.positions.items():
                current_price = position['current_price']
                position_value = position['quantity'] * current_price
                position_pnl = (current_price - position['avg_price']) * position['quantity']
                position_pnl_percent = (position_pnl / (position['avg_price'] * position['quantity'])) * 100
                
                print(f"  {symbol}: {position['quantity']} shares @ ${position['avg_price']:.2f} "
                      f"(current: ${current_price:.2f}, P&L: ${position_pnl:+.2f} {position_pnl_percent:+.1f}%)")

def run_demo_trading_session(duration_minutes: int = 10):
    """Run a demo trading session"""
    
    print("🚀 STARTING DEMO AI TRADING SESSION")
    print("=" * 50)
    print(f"Duration: {duration_minutes} minutes")
    print("This demo simulates AI-powered trading without requiring Ollama")
    print()
    
    # Initialize trading system
    trading_system = MockTradingSystem()
    
    # Trading parameters
    symbols = list(trading_system.market_prices.keys())
    confidence_threshold = 0.7
    max_position_size = 0.1  # 10% of portfolio per position
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    cycle_count = 0
    
    try:
        while time.time() < end_time:
            cycle_count += 1
            print(f"\n🔄 Trading Cycle {cycle_count}")
            print("-" * 30)
            
            # Select random symbol for analysis
            symbol = random.choice(symbols)
            
            # Get AI consensus
            consensus = trading_system.get_ai_consensus(symbol)
            
            print(f"\n📊 Consensus for {symbol}:")
            print(f"  Action: {consensus['consensus_action']}")
            print(f"  Strength: {consensus['consensus_strength']:.2f}")
            print(f"  Confidence: {consensus['average_confidence']:.2f}")
            print(f"  Votes: {consensus['vote_breakdown']}")
            
            # Execute trade if consensus is strong enough
            if (consensus['consensus_strength'] >= 0.6 and 
                consensus['average_confidence'] >= confidence_threshold and
                consensus['consensus_action'] in ['BUY', 'SELL']):
                
                # Calculate position size
                portfolio_value = trading_system.get_portfolio_value()
                max_trade_value = portfolio_value * max_position_size
                current_price = trading_system.get_market_price(symbol)
                quantity = int(max_trade_value / current_price)
                
                if quantity > 0:
                    reasoning = f"AI consensus: {consensus['consensus_action']} with {consensus['consensus_strength']:.1%} agreement"
                    
                    success = trading_system.execute_trade(
                        symbol, consensus['consensus_action'], quantity, reasoning
                    )
                    
                    if success:
                        print(f"  ✅ Executed: {consensus['consensus_action']} {quantity} {symbol}")
                    else:
                        print(f"  ❌ Trade failed: {consensus['consensus_action']} {symbol}")
                else:
                    print(f"  ⏸️ Position size too small for {symbol}")
            else:
                print(f"  ⏸️ No trade: Consensus not strong enough")
            
            # Wait before next cycle
            remaining_time = end_time - time.time()
            if remaining_time > 0:
                wait_time = min(30, remaining_time)  # 30 second cycles
                print(f"  ⏳ Waiting {wait_time:.0f}s for next cycle...")
                time.sleep(wait_time)
                
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    
    # Final summary
    print(f"\n🎯 DEMO COMPLETE")
    print(f"Duration: {(time.time() - start_time)/60:.1f} minutes")
    print(f"Cycles: {cycle_count}")
    
    trading_system.print_portfolio_summary()
    
    return trading_system

def main():
    """Main demo function"""
    
    print("🎯 AI TRADING SYSTEM DEMO (NO OLLAMA REQUIRED)")
    print("=" * 60)
    print("This demo shows how the AI trading system works using mock AI agents.")
    print("The actual system uses real AI models via Ollama for analysis.")
    print()
    
    # Get demo duration
    try:
        duration = input("Demo duration in minutes (default 5): ").strip()
        duration = int(duration) if duration else 5
    except:
        duration = 5
    
    # Run demo
    trading_system = run_demo_trading_session(duration)
    
    # Save demo results
    demo_results = {
        'timestamp': datetime.now().isoformat(),
        'duration_minutes': duration,
        'initial_capital': trading_system.initial_capital,
        'final_value': trading_system.get_portfolio_value(),
        'total_trades': len(trading_system.trades),
        'positions': trading_system.positions,
        'trades': trading_system.trades
    }
    
    filename = f"demo_results_{int(time.time())}.json"
    with open(filename, 'w') as f:
        json.dump(demo_results, f, indent=2)
    
    print(f"\n📄 Demo results saved to: {filename}")
    print("\n🚀 To run with real AI models:")
    print("  1. Install Ollama: https://ollama.ai")
    print("  2. Start Ollama: ollama serve")
    print("  3. Pull models: ollama pull qwen2.5:7b")
    print("  4. Run: python start_ai_paper_trading.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nDemo cancelled by user.")
    except Exception as e:
        print(f"\nDemo error: {e}")
