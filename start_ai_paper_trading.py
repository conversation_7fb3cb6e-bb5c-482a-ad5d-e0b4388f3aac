#!/usr/bin/env python3
"""
START AI PAPER TRADING
Launch the AI-powered paper trading system
"""

import os
import sys
import time
import json
import asyncio
import sqlite3
import subprocess
from datetime import datetime
from typing import Dict, List, Any

# Import system components
try:
    from simplified_paper_trading import SimplifiedPaperTrading
    from enhanced_ai_team_with_fathom import EnhancedAITeamWithFathom
    from working_risk_management import WorkingRiskManager
    from live_portfolio_tracker import LivePortfolioTracker
except ImportError as e:
    print(f"⚠️ Import warning: {e}")
    print("Some components may not be available.")

class AIPaperTradingLauncher:
    """AI Paper Trading System Launcher"""
    
    def __init__(self):
        self.config = {
            'initial_capital': 100000.0,
            'max_position_size': 0.1,  # 10% max per position
            'confidence_threshold': 0.7,
            'trading_symbols': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX'],
            'update_interval': 60,  # seconds
            'max_trades_per_day': 20
        }
        
        self.paper_trading = None
        self.ai_team = None
        self.risk_manager = None
        self.portfolio_tracker = None
        self.is_running = False
        
    def initialize_systems(self) -> bool:
        """Initialize all trading systems"""
        
        print("🚀 INITIALIZING AI PAPER TRADING SYSTEM")
        print("=" * 50)
        
        try:
            # Initialize paper trading
            print("💹 Initializing paper trading system...")
            self.paper_trading = SimplifiedPaperTrading()
            print("  ✅ Paper trading system ready")
            
            # Initialize AI team
            print("🤖 Initializing AI team...")
            self.ai_team = EnhancedAITeamWithFathom()
            print(f"  ✅ AI team ready: {len(self.ai_team.ai_team)} agents")
            
            # Initialize risk manager
            print("🛡️ Initializing risk management...")
            self.risk_manager = WorkingRiskManager(self.config['initial_capital'])
            print("  ✅ Risk management ready")
            
            # Initialize portfolio tracker
            print("📊 Initializing portfolio tracker...")
            self.portfolio_tracker = LivePortfolioTracker(self.config['initial_capital'])
            print("  ✅ Portfolio tracker ready")
            
            # Verify database
            if not self.verify_database():
                print("  ⚠️ Database verification failed, initializing...")
                self.initialize_database()
            
            print("\n✅ All systems initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
            
    def verify_database(self) -> bool:
        """Verify paper trading database"""
        try:
            conn = sqlite3.connect('paper_trading_simplified.db')
            cursor = conn.cursor()
            
            # Check if portfolio exists
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='performance'")
            has_tables = cursor.fetchone()[0] > 0
            
            conn.close()
            return has_tables
            
        except Exception:
            return False
            
    def initialize_database(self):
        """Initialize paper trading database"""
        try:
            conn = sqlite3.connect('paper_trading_simplified.db')
            cursor = conn.cursor()
            
            # Create performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance (
                    id INTEGER PRIMARY KEY,
                    timestamp TEXT,
                    capital REAL,
                    total_pnl REAL,
                    positions_count INTEGER,
                    win_rate REAL
                )
            ''')
            
            # Insert initial performance record
            cursor.execute('''
                INSERT OR REPLACE INTO performance (id, timestamp, capital, total_pnl, positions_count, win_rate)
                VALUES (1, ?, ?, 0.0, 0, 0.0)
            ''', (datetime.now().isoformat(), self.config['initial_capital']))
            
            conn.commit()
            conn.close()
            
            print("  ✅ Database initialized")
            
        except Exception as e:
            print(f"  ❌ Database initialization failed: {e}")
            
    async def get_ai_trading_signal(self, symbol: str) -> Dict[str, Any]:
        """Get AI trading signal for a symbol"""
        
        try:
            # Query AI team for trading recommendation
            query = f"""
            Analyze {symbol} and provide a trading recommendation.
            Consider:
            - Current market conditions
            - Technical indicators
            - Risk factors
            - Position sizing
            
            Respond with: BUY, SELL, or HOLD
            Include confidence level (0-1) and brief reasoning.
            """
            
            # Get first available agent
            agent_name = list(self.ai_team.ai_team.keys())[0]
            agent_info = self.ai_team.ai_team[agent_name]
            
            # Call AI model
            result = subprocess.run([
                'ollama', 'run', agent_info['model'], query
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                response = result.stdout.strip()
                
                # Parse response (simple parsing)
                action = 'HOLD'
                confidence = 0.5
                reasoning = response
                
                if 'BUY' in response.upper():
                    action = 'BUY'
                elif 'SELL' in response.upper():
                    action = 'SELL'
                
                # Extract confidence if mentioned
                import re
                conf_match = re.search(r'confidence[:\s]*([0-9.]+)', response.lower())
                if conf_match:
                    try:
                        confidence = float(conf_match.group(1))
                        if confidence > 1:
                            confidence = confidence / 100  # Convert percentage
                    except:
                        pass
                
                return {
                    'symbol': symbol,
                    'action': action,
                    'confidence': confidence,
                    'reasoning': reasoning[:200],  # Limit reasoning length
                    'agent': agent_name,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"  ⚠️ AI signal error for {symbol}: {e}")
            
        # Default response
        return {
            'symbol': symbol,
            'action': 'HOLD',
            'confidence': 0.0,
            'reasoning': 'AI analysis failed',
            'agent': 'none',
            'timestamp': datetime.now().isoformat()
        }
        
    async def execute_trading_cycle(self):
        """Execute one trading cycle"""
        
        print(f"\n🔄 Trading cycle at {datetime.now().strftime('%H:%M:%S')}")
        
        signals = []
        trades_executed = 0
        
        # Get AI signals for each symbol
        for symbol in self.config['trading_symbols']:
            signal = await self.get_ai_trading_signal(symbol)
            signals.append(signal)
            
            # Execute trade if confidence is high enough
            if (signal['confidence'] >= self.config['confidence_threshold'] and 
                signal['action'] in ['BUY', 'SELL'] and
                trades_executed < self.config['max_trades_per_day']):
                
                try:
                    # Calculate position size (simple fixed size for demo)
                    quantity = 10  # 10 shares
                    
                    if signal['action'] == 'BUY':
                        success = self.paper_trading.execute_buy(symbol, quantity, signal['reasoning'])
                    else:
                        success = self.paper_trading.execute_sell(symbol, quantity, signal['reasoning'])
                    
                    if success:
                        trades_executed += 1
                        print(f"  ✅ {signal['action']} {quantity} {symbol} (confidence: {signal['confidence']:.2f})")
                    else:
                        print(f"  ❌ Failed to {signal['action']} {symbol}")
                        
                except Exception as e:
                    print(f"  ⚠️ Trade execution error for {symbol}: {e}")
            else:
                print(f"  ⏸️ {symbol}: {signal['action']} (confidence: {signal['confidence']:.2f}) - Below threshold")
        
        # Update portfolio
        try:
            portfolio_status = self.paper_trading.get_portfolio_summary()
            print(f"\n📊 Portfolio: ${portfolio_status.get('total_value', 0):,.2f} total, "
                  f"${portfolio_status.get('cash', 0):,.2f} cash, "
                  f"{portfolio_status.get('positions', 0)} positions")
        except Exception as e:
            print(f"  ⚠️ Portfolio update error: {e}")
        
        return {
            'signals': signals,
            'trades_executed': trades_executed,
            'timestamp': datetime.now().isoformat()
        }
        
    async def run_trading_session(self, duration_minutes: int = 60):
        """Run trading session for specified duration"""
        
        print(f"\n🚀 STARTING AI PAPER TRADING SESSION")
        print(f"Duration: {duration_minutes} minutes")
        print(f"Update interval: {self.config['update_interval']} seconds")
        print(f"Trading symbols: {', '.join(self.config['trading_symbols'])}")
        print(f"Confidence threshold: {self.config['confidence_threshold']}")
        print()
        
        self.is_running = True
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        cycle_count = 0
        
        try:
            while self.is_running and time.time() < end_time:
                cycle_count += 1
                
                # Execute trading cycle
                cycle_results = await self.execute_trading_cycle()
                
                # Wait for next cycle
                remaining_time = end_time - time.time()
                if remaining_time > 0:
                    wait_time = min(self.config['update_interval'], remaining_time)
                    print(f"  ⏳ Waiting {wait_time:.0f}s for next cycle...")
                    await asyncio.sleep(wait_time)
                    
        except KeyboardInterrupt:
            print("\n⚠️ Trading session interrupted by user")
            self.is_running = False
            
        # Final summary
        total_time = time.time() - start_time
        print(f"\n📊 TRADING SESSION COMPLETE")
        print(f"Duration: {total_time/60:.1f} minutes")
        print(f"Cycles completed: {cycle_count}")
        
        try:
            final_portfolio = self.paper_trading.get_portfolio_summary()
            print(f"Final portfolio value: ${final_portfolio.get('total_value', 0):,.2f}")
            
            initial_value = self.config['initial_capital']
            current_value = final_portfolio.get('total_value', initial_value)
            pnl = current_value - initial_value
            pnl_percent = (pnl / initial_value) * 100
            
            print(f"P&L: ${pnl:,.2f} ({pnl_percent:+.2f}%)")
            
        except Exception as e:
            print(f"⚠️ Final summary error: {e}")

def main():
    """Main function"""
    
    # Create launcher
    launcher = AIPaperTradingLauncher()
    
    # Initialize systems
    if not launcher.initialize_systems():
        print("❌ System initialization failed")
        return False
    
    # Get session parameters
    print("\n🎯 TRADING SESSION CONFIGURATION")
    print("=" * 40)
    
    try:
        duration = input("Session duration in minutes (default 60): ").strip()
        duration = int(duration) if duration else 60
    except:
        duration = 60
    
    print(f"Starting {duration}-minute trading session...")
    
    # Run trading session
    try:
        asyncio.run(launcher.run_trading_session(duration))
        print("\n✅ Trading session completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Trading session failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTrading session cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nFatal error: {e}")
        sys.exit(1)
