#!/usr/bin/env python3
"""
Simple Paper Trading Launcher
Launches paper trading without complex dependencies
"""

import asyncio
import json
import time
import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

@dataclass
class TradingPosition:
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    pnl: float
    timestamp: datetime

@dataclass
class TradingSignal:
    symbol: str
    action: str
    confidence: float
    price_target: float
    stop_loss: float
    reasoning: str
    timestamp: datetime

class SimplePaperTrading:
    """Simple paper trading system without complex dependencies"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: Dict[str, TradingPosition] = {}
        self.trade_history: List[Dict] = []
        self.db_path = "simple_paper_trading.db"
        self.setup_database()
        
        console.print(Panel(
            f"[bold green]📈 SIMPLE PAPER TRADING SYSTEM[/bold green]\n\n"
            f"Initial Capital: ${initial_capital:,.2f}\n"
            f"Database: {self.db_path}",
            title="Paper Trading Initialized"
        ))
    
    def setup_database(self):
        """Setup SQLite database for paper trading"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                timestamp DATETIME,
                reasoning TEXT,
                pnl REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio (
                id INTEGER PRIMARY KEY,
                symbol TEXT UNIQUE,
                quantity REAL,
                avg_price REAL,
                current_price REAL,
                pnl REAL,
                last_updated DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        console.print("✅ Database initialized")
    
    def get_mock_price(self, symbol: str) -> float:
        """Get mock price for testing (in real system, use actual market data)"""
        import random
        base_prices = {
            "AAPL": 180.0,
            "GOOGL": 140.0,
            "MSFT": 380.0,
            "TSLA": 250.0,
            "NVDA": 800.0,
            "AMZN": 150.0,
            "META": 350.0,
            "BTC": 45000.0,
            "ETH": 3000.0
        }
        base_price = base_prices.get(symbol, 100.0)
        # Add some random variation
        variation = random.uniform(-0.05, 0.05)  # ±5%
        return base_price * (1 + variation)
    
    def execute_trade(self, signal: TradingSignal) -> Dict[str, Any]:
        """Execute a paper trade based on signal"""
        current_price = self.get_mock_price(signal.symbol)
        
        if signal.action.upper() == "BUY":
            return self._execute_buy(signal, current_price)
        elif signal.action.upper() == "SELL":
            return self._execute_sell(signal, current_price)
        else:
            return {"success": False, "error": "Invalid action"}
    
    def _execute_buy(self, signal: TradingSignal, current_price: float) -> Dict[str, Any]:
        """Execute buy order"""
        # Calculate position size (risk 2% of capital)
        risk_amount = self.current_capital * 0.02
        stop_distance = abs(current_price - signal.stop_loss)
        
        if stop_distance > 0:
            quantity = risk_amount / stop_distance
        else:
            quantity = self.current_capital * 0.05 / current_price  # 5% of capital
        
        # Limit quantity to available capital
        max_quantity = (self.current_capital * 0.1) / current_price  # Max 10% per position
        quantity = min(quantity, max_quantity)
        
        cost = quantity * current_price
        
        if cost > self.current_capital:
            return {"success": False, "error": "Insufficient capital"}
        
        # Update portfolio
        if signal.symbol in self.positions:
            # Add to existing position
            existing = self.positions[signal.symbol]
            total_quantity = existing.quantity + quantity
            avg_price = ((existing.quantity * existing.entry_price) + cost) / total_quantity
            
            self.positions[signal.symbol] = TradingPosition(
                symbol=signal.symbol,
                quantity=total_quantity,
                entry_price=avg_price,
                current_price=current_price,
                pnl=(current_price - avg_price) * total_quantity,
                timestamp=datetime.now()
            )
        else:
            # New position
            self.positions[signal.symbol] = TradingPosition(
                symbol=signal.symbol,
                quantity=quantity,
                entry_price=current_price,
                current_price=current_price,
                pnl=0.0,
                timestamp=datetime.now()
            )
        
        self.current_capital -= cost
        
        # Record trade
        trade_record = {
            "symbol": signal.symbol,
            "action": "BUY",
            "quantity": quantity,
            "price": current_price,
            "cost": cost,
            "reasoning": signal.reasoning,
            "timestamp": datetime.now()
        }
        
        self.trade_history.append(trade_record)
        self._save_trade_to_db(trade_record)
        
        return {
            "success": True,
            "action": "BUY",
            "symbol": signal.symbol,
            "quantity": quantity,
            "price": current_price,
            "cost": cost,
            "remaining_capital": self.current_capital
        }
    
    def _execute_sell(self, signal: TradingSignal, current_price: float) -> Dict[str, Any]:
        """Execute sell order"""
        if signal.symbol not in self.positions:
            return {"success": False, "error": "No position to sell"}
        
        position = self.positions[signal.symbol]
        quantity = position.quantity
        proceeds = quantity * current_price
        pnl = (current_price - position.entry_price) * quantity
        
        # Remove position
        del self.positions[signal.symbol]
        self.current_capital += proceeds
        
        # Record trade
        trade_record = {
            "symbol": signal.symbol,
            "action": "SELL",
            "quantity": quantity,
            "price": current_price,
            "proceeds": proceeds,
            "pnl": pnl,
            "reasoning": signal.reasoning,
            "timestamp": datetime.now()
        }
        
        self.trade_history.append(trade_record)
        self._save_trade_to_db(trade_record)
        
        return {
            "success": True,
            "action": "SELL",
            "symbol": signal.symbol,
            "quantity": quantity,
            "price": current_price,
            "proceeds": proceeds,
            "pnl": pnl,
            "total_capital": self.current_capital
        }
    
    def _save_trade_to_db(self, trade: Dict):
        """Save trade to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO trades (symbol, action, quantity, price, timestamp, reasoning, pnl)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade["symbol"],
            trade["action"],
            trade["quantity"],
            trade["price"],
            trade["timestamp"].isoformat(),
            trade["reasoning"],
            trade.get("pnl", 0.0)
        ))
        
        conn.commit()
        conn.close()
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary"""
        total_value = self.current_capital
        total_pnl = 0.0
        
        # Update current prices and calculate PnL
        for symbol, position in self.positions.items():
            current_price = self.get_mock_price(symbol)
            position.current_price = current_price
            position.pnl = (current_price - position.entry_price) * position.quantity
            total_value += position.quantity * current_price
            total_pnl += position.pnl
        
        return {
            "initial_capital": self.initial_capital,
            "current_cash": self.current_capital,
            "positions_value": total_value - self.current_capital,
            "total_value": total_value,
            "total_pnl": total_pnl,
            "total_return": (total_value - self.initial_capital) / self.initial_capital,
            "positions": {symbol: {
                "quantity": pos.quantity,
                "entry_price": pos.entry_price,
                "current_price": pos.current_price,
                "pnl": pos.pnl,
                "pnl_percent": pos.pnl / (pos.entry_price * pos.quantity) if pos.quantity > 0 else 0
            } for symbol, pos in self.positions.items()}
        }
    
    def display_portfolio(self):
        """Display current portfolio status"""
        summary = self.get_portfolio_summary()
        
        # Portfolio summary
        console.print(Panel(
            f"[bold blue]💰 PORTFOLIO SUMMARY[/bold blue]\n\n"
            f"Initial Capital: ${summary['initial_capital']:,.2f}\n"
            f"Current Cash: ${summary['current_cash']:,.2f}\n"
            f"Positions Value: ${summary['positions_value']:,.2f}\n"
            f"Total Value: ${summary['total_value']:,.2f}\n"
            f"Total P&L: ${summary['total_pnl']:,.2f}\n"
            f"Total Return: {summary['total_return']:.2%}",
            title="Portfolio Status"
        ))
        
        # Positions table
        if summary['positions']:
            positions_table = Table(title="Current Positions")
            positions_table.add_column("Symbol", style="cyan")
            positions_table.add_column("Quantity", style="yellow")
            positions_table.add_column("Entry Price", style="green")
            positions_table.add_column("Current Price", style="blue")
            positions_table.add_column("P&L", style="red")
            positions_table.add_column("P&L %", style="magenta")
            
            for symbol, pos in summary['positions'].items():
                pnl_color = "green" if pos['pnl'] >= 0 else "red"
                positions_table.add_row(
                    symbol,
                    f"{pos['quantity']:.2f}",
                    f"${pos['entry_price']:.2f}",
                    f"${pos['current_price']:.2f}",
                    f"[{pnl_color}]${pos['pnl']:,.2f}[/{pnl_color}]",
                    f"[{pnl_color}]{pos['pnl_percent']:.2%}[/{pnl_color}]"
                )
            
            console.print(positions_table)

def demo_trading_session():
    """Run a demo trading session"""
    console.print(Panel(
        "[bold blue]🚀 STARTING DEMO TRADING SESSION[/bold blue]\n\n"
        "This will simulate AI-driven trading decisions...",
        title="Demo Session"
    ))
    
    # Initialize paper trading
    trader = SimplePaperTrading(initial_capital=100000)
    
    # Sample trading signals (in real system, these come from AI models)
    demo_signals = [
        TradingSignal("AAPL", "BUY", 0.85, 185.0, 175.0, "Strong technical setup with AI confidence 85%", datetime.now()),
        TradingSignal("GOOGL", "BUY", 0.78, 145.0, 135.0, "Positive earnings outlook, AI confidence 78%", datetime.now()),
        TradingSignal("TSLA", "BUY", 0.72, 260.0, 240.0, "EV market growth signals, AI confidence 72%", datetime.now()),
    ]
    
    # Execute trades
    for signal in demo_signals:
        console.print(f"\n[yellow]Executing signal: {signal.action} {signal.symbol}[/yellow]")
        result = trader.execute_trade(signal)
        
        if result["success"]:
            console.print(f"✅ {result['action']} {result['quantity']:.2f} shares of {result['symbol']} at ${result['price']:.2f}")
        else:
            console.print(f"❌ Trade failed: {result['error']}")
        
        time.sleep(1)  # Simulate time between trades
    
    # Display final portfolio
    console.print("\n" + "="*60)
    trader.display_portfolio()
    
    return trader

if __name__ == "__main__":
    demo_trading_session()
