#!/usr/bin/env python3
"""
BUILD EXISTING MODELS
Build your existing Modelfiles into working Ollama models
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def check_ollama_registry():
    """Check what's actually in Ollama registry"""
    
    print("🔍 Checking Ollama registry...")
    
    try:
        # Try multiple approaches to get model list
        methods = [
            ['ollama', 'list'],
            ['ollama', 'ps'],
            ['curl', '-s', 'http://localhost:11434/api/tags']
        ]
        
        for method in methods:
            try:
                result = subprocess.run(method, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and result.stdout.strip():
                    print(f"  ✅ Method {' '.join(method)}: Success")
                    print(f"  📄 Output: {result.stdout[:200]}...")
                    return result.stdout
                else:
                    print(f"  ❌ Method {' '.join(method)}: Failed")
                    
            except Exception as e:
                print(f"  ⚠️ Method {' '.join(method)}: {e}")
                
        print("  ⚠️ All methods failed, but Ollama is running")
        return ""
        
    except Exception as e:
        print(f"  ❌ Registry check failed: {e}")
        return ""

def find_your_modelfiles():
    """Find your existing Modelfiles"""
    
    print("\n📁 Finding your Modelfiles...")
    
    modelfiles = []
    
    for file in os.listdir('.'):
        if file.startswith('Modelfile.'):
            model_name = file.replace('Modelfile.', '')
            modelfiles.append({
                'filename': file,
                'model_name': model_name,
                'path': os.path.abspath(file)
            })
    
    # Sort by priority (finance models first)
    finance_models = [m for m in modelfiles if 'finance' in m['model_name'].lower()]
    other_models = [m for m in modelfiles if 'finance' not in m['model_name'].lower()]
    
    priority_models = finance_models + other_models
    
    print(f"  📊 Found {len(modelfiles)} Modelfiles")
    print(f"  💹 Finance models: {len(finance_models)}")
    print(f"  🔧 Other models: {len(other_models)}")
    
    return priority_models

def build_model_from_modelfile(modelfile_info):
    """Build a single model from its Modelfile"""
    
    filename = modelfile_info['filename']
    model_name = modelfile_info['model_name']
    
    print(f"  🔨 Building {model_name}...")
    
    try:
        # Build the model using ollama create
        result = subprocess.run([
            'ollama', 'create', model_name, '-f', filename
        ], capture_output=True, text=True, timeout=120)  # 2 minute timeout
        
        if result.returncode == 0:
            print(f"    ✅ {model_name}: Built successfully")
            return True
        else:
            error_msg = result.stderr.strip() if result.stderr else "Unknown error"
            print(f"    ❌ {model_name}: Build failed - {error_msg[:100]}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"    ⏰ {model_name}: Build timed out")
        return False
    except Exception as e:
        print(f"    ❌ {model_name}: Error - {e}")
        return False

def test_built_model(model_name):
    """Test if a built model works"""
    
    try:
        result = subprocess.run([
            'ollama', 'run', model_name, 'Test: What is 2+2? Answer briefly.'
        ], capture_output=True, text=True, timeout=20)
        
        if result.returncode == 0 and len(result.stdout.strip()) > 0:
            return True
        else:
            return False
            
    except Exception:
        return False

def build_priority_models(modelfiles, max_models=10):
    """Build priority models (finance models first)"""
    
    print(f"\n🔨 Building priority models (max {max_models})...")
    
    built_models = []
    failed_models = []
    
    # Focus on finance models first
    finance_models = [m for m in modelfiles if 'finance' in m['model_name'].lower()]
    
    # Select top priority models
    priority_list = finance_models[:max_models//2]  # Half finance
    
    # Add some non-finance models
    other_models = [m for m in modelfiles if 'finance' not in m['model_name'].lower()]
    priority_list.extend(other_models[:max_models//2])
    
    print(f"  🎯 Selected {len(priority_list)} priority models to build")
    
    for i, modelfile_info in enumerate(priority_list):
        model_name = modelfile_info['model_name']
        
        print(f"\n  [{i+1}/{len(priority_list)}] Building {model_name}...")
        
        # Build the model
        if build_model_from_modelfile(modelfile_info):
            # Test the model
            print(f"    🧪 Testing {model_name}...")
            
            if test_built_model(model_name):
                print(f"    ✅ {model_name}: Working!")
                built_models.append(model_name)
            else:
                print(f"    ⚠️ {model_name}: Built but not responding")
                failed_models.append(model_name)
        else:
            failed_models.append(model_name)
    
    return built_models, failed_models

def create_working_ai_teams(working_models):
    """Create AI teams from working models"""
    
    print(f"\n👥 Creating AI teams from {len(working_models)} working models...")
    
    teams = {
        'finance_specialists': [],
        'risk_managers': [],
        'technical_analysts': [],
        'general_purpose': []
    }
    
    for model in working_models:
        model_lower = model.lower()
        
        if 'finance' in model_lower:
            teams['finance_specialists'].append(model)
            print(f"  🏦 {model} → Finance Specialists")
        elif any(keyword in model_lower for keyword in ['risk', 'deepseek', 'cogito']):
            teams['risk_managers'].append(model)
            print(f"  🛡️ {model} → Risk Managers")
        elif any(keyword in model_lower for keyword in ['phi', 'gemma', 'technical']):
            teams['technical_analysts'].append(model)
            print(f"  📊 {model} → Technical Analysts")
        else:
            teams['general_purpose'].append(model)
            print(f"  🔧 {model} → General Purpose")
    
    return teams

def main():
    """Main function to build your existing models"""
    
    print("🔨 BUILD YOUR EXISTING AI MODELS")
    print("=" * 50)
    print("Building your 137 existing models into working Ollama models...")
    print()
    
    # Step 1: Check current Ollama registry
    registry_output = check_ollama_registry()
    
    # Step 2: Find your Modelfiles
    modelfiles = find_your_modelfiles()
    
    if not modelfiles:
        print("❌ No Modelfiles found!")
        return False
    
    # Step 3: Build priority models
    print(f"\n🎯 Building priority models from your {len(modelfiles)} Modelfiles...")
    
    built_models, failed_models = build_priority_models(modelfiles, max_models=10)
    
    # Step 4: Create teams
    if built_models:
        teams = create_working_ai_teams(built_models)
        
        # Step 5: Summary
        print(f"\n" + "=" * 50)
        print(f"🎉 MODEL BUILDING COMPLETE!")
        print(f"=" * 50)
        
        print(f"✅ Successfully built: {len(built_models)} models")
        for model in built_models:
            print(f"  🤖 {model}")
        
        if failed_models:
            print(f"\n⚠️ Failed to build: {len(failed_models)} models")
            for model in failed_models[:5]:  # Show first 5
                print(f"  ❌ {model}")
            if len(failed_models) > 5:
                print(f"  ... and {len(failed_models) - 5} more")
        
        print(f"\n👥 AI Teams:")
        for team_name, models in teams.items():
            if models:
                print(f"  {team_name.replace('_', ' ').title()}: {len(models)} models")
        
        print(f"\n🚀 Your AI trading system is now ready!")
        print(f"Start trading with: python start_ai_paper_trading.py")
        
        # Save results
        with open('built_models.json', 'w') as f:
            import json
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'built_models': built_models,
                'failed_models': failed_models,
                'teams': teams,
                'total_modelfiles': len(modelfiles)
            }, f, indent=2)
        
        print(f"📄 Results saved to: built_models.json")
        
        return True
    else:
        print("❌ No models could be built successfully.")
        print("This might indicate:")
        print("  • Missing base models that Modelfiles reference")
        print("  • Ollama service issues")
        print("  • Modelfile syntax problems")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nBuild cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nBuild error: {e}")
        sys.exit(1)
