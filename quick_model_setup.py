#!/usr/bin/env python3
"""
QUICK MODEL SETUP
Get basic AI models working immediately for trading
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def pull_basic_models():
    """Pull basic models to get started quickly"""
    
    print("🚀 QUICK MODEL SETUP")
    print("=" * 40)
    print("Setting up basic AI models for immediate trading...")
    print()
    
    # Basic models that should work immediately
    basic_models = [
        'qwen2.5:7b',
        'phi3:mini',
        'gemma2:9b',
        'llama3.2:3b'
    ]
    
    successful_models = []
    failed_models = []
    
    for model in basic_models:
        print(f"📥 Pulling {model}...")
        
        try:
            result = subprocess.run([
                'ollama', 'pull', model
            ], capture_output=True, text=True, timeout=300)  # 5 minute timeout
            
            if result.returncode == 0:
                print(f"  ✅ {model}: Successfully pulled")
                successful_models.append(model)
            else:
                print(f"  ❌ {model}: Pull failed")
                failed_models.append(model)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {model}: Pull timed out")
            failed_models.append(model)
        except Exception as e:
            print(f"  ❌ {model}: Error - {e}")
            failed_models.append(model)
    
    print(f"\n📊 Pull Results:")
    print(f"  ✅ Successful: {len(successful_models)}")
    print(f"  ❌ Failed: {len(failed_models)}")
    
    return successful_models

def test_models(models):
    """Test the pulled models"""
    
    print(f"\n🧪 Testing {len(models)} models...")
    
    working_models = []
    
    for model in models:
        print(f"  🔍 Testing {model}...")
        
        try:
            result = subprocess.run([
                'ollama', 'run', model, 
                'Analyze AAPL stock. Provide: Action (BUY/SELL/HOLD), Confidence (0-1), Brief reason.'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and len(result.stdout.strip()) > 20:
                response = result.stdout.strip()
                print(f"    ✅ {model}: Working ({len(response)} chars)")
                working_models.append({
                    'name': model,
                    'response_length': len(response),
                    'sample_response': response[:100] + '...' if len(response) > 100 else response
                })
            else:
                print(f"    ❌ {model}: No valid response")
                
        except Exception as e:
            print(f"    ❌ {model}: Test failed - {e}")
    
    return working_models

def create_ai_teams(working_models):
    """Create AI teams from working models"""
    
    print(f"\n👥 Creating AI teams from {len(working_models)} working models...")
    
    teams = {
        'finance_team': [],
        'risk_team': [],
        'analysis_team': [],
        'consensus_team': working_models  # All models participate in consensus
    }
    
    # Assign models to specialized teams based on their characteristics
    for model in working_models:
        model_name = model['name'].lower()
        
        if 'qwen' in model_name:
            teams['finance_team'].append(model)
            print(f"  🏦 {model['name']} → Finance Team")
        elif 'phi' in model_name:
            teams['risk_team'].append(model)
            print(f"  🛡️ {model['name']} → Risk Team")
        elif 'gemma' in model_name:
            teams['analysis_team'].append(model)
            print(f"  📊 {model['name']} → Analysis Team")
        else:
            teams['finance_team'].append(model)
            print(f"  🏦 {model['name']} → Finance Team (default)")
    
    return teams

def run_team_demo(teams):
    """Run a quick team demonstration"""
    
    print(f"\n🎯 Running AI team demonstration...")
    
    symbol = 'AAPL'
    query = f"Provide trading analysis for {symbol}: Action (BUY/SELL/HOLD), Confidence (0-1), Reason."
    
    team_results = {}
    
    for team_name, models in teams.items():
        if not models or team_name == 'consensus_team':
            continue
            
        print(f"\n  🔄 {team_name.replace('_', ' ').title()} Analysis:")
        
        team_responses = []
        
        for model in models[:2]:  # Test first 2 models per team
            model_name = model['name']
            print(f"    🤖 Querying {model_name}...")
            
            try:
                result = subprocess.run([
                    'ollama', 'run', model_name, query
                ], capture_output=True, text=True, timeout=25)
                
                if result.returncode == 0 and result.stdout.strip():
                    response = result.stdout.strip()
                    team_responses.append({
                        'model': model_name,
                        'response': response[:150] + '...' if len(response) > 150 else response
                    })
                    print(f"      ✅ Response received")
                else:
                    print(f"      ❌ No response")
                    
            except Exception as e:
                print(f"      ❌ Error: {e}")
        
        team_results[team_name] = team_responses
    
    return team_results

def main():
    """Main setup function"""
    
    print("🎯 QUICK AI MODEL SETUP FOR TRADING")
    print("This will get you started with basic AI models immediately!")
    print()
    
    # Step 1: Pull basic models
    successful_models = pull_basic_models()
    
    if not successful_models:
        print("❌ No models could be pulled. Check your internet connection and Ollama installation.")
        return False
    
    # Step 2: Test models
    working_models = test_models(successful_models)
    
    if not working_models:
        print("❌ No models are responding. There may be an issue with Ollama.")
        return False
    
    # Step 3: Create teams
    teams = create_ai_teams(working_models)
    
    # Step 4: Run demo
    demo_results = run_team_demo(teams)
    
    # Step 5: Summary
    print(f"\n" + "=" * 50)
    print(f"🎉 QUICK SETUP COMPLETE!")
    print(f"=" * 50)
    
    print(f"✅ Working Models: {len(working_models)}")
    for model in working_models:
        print(f"  🤖 {model['name']}")
    
    print(f"\n👥 AI Teams Created:")
    for team_name, models in teams.items():
        if models and team_name != 'consensus_team':
            print(f"  {team_name.replace('_', ' ').title()}: {len(models)} models")
    
    print(f"\n🚀 Ready for Trading!")
    print(f"You can now run:")
    print(f"  • python start_ai_paper_trading.py")
    print(f"  • python simplified_paper_trading.py")
    print(f"  • python demo_without_ollama.py")
    
    # Save working models info
    with open('working_models.json', 'w') as f:
        import json
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'working_models': working_models,
            'teams': {k: [m['name'] for m in v] for k, v in teams.items()},
            'demo_results': demo_results
        }, f, indent=2)
    
    print(f"\n📄 Model info saved to: working_models.json")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nSetup error: {e}")
        sys.exit(1)
