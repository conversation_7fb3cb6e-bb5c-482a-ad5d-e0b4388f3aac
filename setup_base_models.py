#!/usr/bin/env python3
"""
SETUP BASE MODELS
Identify and setup the base models needed for your Modelfiles
"""

import os
import sys
import re
import subprocess
from collections import Counter
from datetime import datetime

def analyze_modelfiles():
    """Analyze all Modelfiles to find required base models"""
    
    print("🔍 Analyzing your Modelfiles to find required base models...")
    
    base_models = []
    modelfile_analysis = {}
    
    for file in os.listdir('.'):
        if file.startswith('Modelfile.'):
            model_name = file.replace('Modelfile.', '')
            
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find FROM statements
                from_matches = re.findall(r'FROM\s+([^\s\n]+)', content, re.IGNORECASE)
                
                if from_matches:
                    base_model = from_matches[0]
                    base_models.append(base_model)
                    
                    modelfile_analysis[model_name] = {
                        'base_model': base_model,
                        'file': file,
                        'has_system_prompt': 'SYSTEM' in content.upper(),
                        'has_parameters': 'PARAMETER' in content.upper()
                    }
                    
            except Exception as e:
                print(f"  ⚠️ Error reading {file}: {e}")
    
    # Count base model usage
    base_model_counts = Counter(base_models)
    
    print(f"  📊 Analyzed {len(modelfile_analysis)} Modelfiles")
    print(f"  🎯 Found {len(base_model_counts)} unique base models needed")
    
    return modelfile_analysis, base_model_counts

def check_available_models():
    """Check what models are already available in Ollama"""
    
    print("\n🔍 Checking available models in Ollama...")
    
    available_models = []
    
    try:
        # Try to get model list
        result = subprocess.run(['ollama', 'list'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    available_models.append(model_name)
            
            print(f"  ✅ Found {len(available_models)} models in Ollama")
            for model in available_models[:10]:  # Show first 10
                print(f"    🤖 {model}")
            if len(available_models) > 10:
                print(f"    ... and {len(available_models) - 10} more")
                
        else:
            print(f"  ⚠️ Could not list models, but Ollama is running")
            
    except Exception as e:
        print(f"  ⚠️ Error checking models: {e}")
    
    return available_models

def identify_missing_base_models(base_model_counts, available_models):
    """Identify which base models are missing"""
    
    print(f"\n🎯 Identifying missing base models...")
    
    missing_models = []
    available_models_set = set(available_models)
    
    for base_model, count in base_model_counts.most_common():
        # Check various forms of the model name
        model_variants = [
            base_model,
            base_model.replace(':latest', ''),
            base_model.replace(':14b', ''),
            base_model.replace(':7b', ''),
            base_model.split(':')[0]  # Just the base name
        ]
        
        found = any(variant in available_models_set for variant in model_variants)
        
        if not found:
            missing_models.append({
                'name': base_model,
                'usage_count': count,
                'suggested_pull': base_model.split(':')[0] if ':' in base_model else base_model
            })
            print(f"  ❌ Missing: {base_model} (used by {count} Modelfiles)")
        else:
            print(f"  ✅ Available: {base_model} (used by {count} Modelfiles)")
    
    return missing_models

def suggest_model_alternatives(missing_models):
    """Suggest alternative models that might work"""
    
    print(f"\n💡 Suggesting alternatives for missing models...")
    
    alternatives = {
        'deepseek-r1': ['deepseek-coder', 'deepseek-chat'],
        'qwen3': ['qwen2.5', 'qwen2'],
        'phi4': ['phi3', 'phi3:mini'],
        'gemma3': ['gemma2', 'gemma'],
        'llama3.3': ['llama3.2', 'llama3.1'],
        'marco-o1': ['qwen2.5', 'deepseek-chat'],
        'cogito': ['qwen2.5', 'phi3']
    }
    
    suggestions = []
    
    for missing in missing_models:
        base_name = missing['name'].split(':')[0].lower()
        
        for alt_base, alt_models in alternatives.items():
            if alt_base in base_name:
                suggestions.append({
                    'missing': missing['name'],
                    'alternatives': alt_models,
                    'usage_count': missing['usage_count']
                })
                print(f"  🔄 {missing['name']} → Try: {', '.join(alt_models)}")
                break
        else:
            # Generic suggestions for unknown models
            generic_alternatives = ['qwen2.5:7b', 'phi3:mini', 'gemma2:9b']
            suggestions.append({
                'missing': missing['name'],
                'alternatives': generic_alternatives,
                'usage_count': missing['usage_count']
            })
            print(f"  🔄 {missing['name']} → Try: {', '.join(generic_alternatives)}")
    
    return suggestions

def create_setup_plan(missing_models, suggestions):
    """Create a setup plan to get the system working"""
    
    print(f"\n📋 Creating setup plan...")
    
    # Prioritize by usage count
    high_priority = [m for m in missing_models if m['usage_count'] >= 5]
    medium_priority = [m for m in missing_models if 2 <= m['usage_count'] < 5]
    low_priority = [m for m in missing_models if m['usage_count'] < 2]
    
    plan = {
        'immediate_actions': [],
        'alternative_models': [],
        'build_priority': []
    }
    
    # Immediate actions - pull common models
    common_models = ['qwen2.5:7b', 'phi3:mini', 'gemma2:9b', 'deepseek-chat']
    plan['immediate_actions'] = [
        f"ollama pull {model}" for model in common_models
    ]
    
    # Alternative models for high priority missing ones
    for suggestion in suggestions:
        if suggestion['usage_count'] >= 3:
            plan['alternative_models'].extend([
                f"ollama pull {alt}" for alt in suggestion['alternatives'][:2]
            ])
    
    # Build priority - start with models that have available base models
    plan['build_priority'] = [
        "Build finance models first (highest value)",
        "Build enhanced models second",
        "Build unrestricted models third"
    ]
    
    return plan

def main():
    """Main analysis function"""
    
    print("🎯 BASE MODEL SETUP ANALYSIS")
    print("=" * 50)
    print("Analyzing your 137 Modelfiles to determine what base models you need...")
    print()
    
    # Step 1: Analyze Modelfiles
    modelfile_analysis, base_model_counts = analyze_modelfiles()
    
    # Step 2: Check available models
    available_models = check_available_models()
    
    # Step 3: Identify missing models
    missing_models = identify_missing_base_models(base_model_counts, available_models)
    
    # Step 4: Suggest alternatives
    suggestions = suggest_model_alternatives(missing_models)
    
    # Step 5: Create setup plan
    plan = create_setup_plan(missing_models, suggestions)
    
    # Summary
    print(f"\n" + "=" * 50)
    print(f"📊 ANALYSIS COMPLETE")
    print(f"=" * 50)
    
    print(f"📁 Modelfiles analyzed: {len(modelfile_analysis)}")
    print(f"🎯 Unique base models needed: {len(base_model_counts)}")
    print(f"✅ Available models: {len(available_models)}")
    print(f"❌ Missing models: {len(missing_models)}")
    
    if missing_models:
        print(f"\n🚀 RECOMMENDED ACTIONS:")
        print(f"\n1. Pull common base models:")
        for action in plan['immediate_actions'][:4]:
            print(f"   {action}")
        
        print(f"\n2. Pull alternatives for your models:")
        for action in list(set(plan['alternative_models']))[:4]:
            print(f"   {action}")
        
        print(f"\n3. Then build your custom models:")
        print(f"   python build_existing_models.py")
        
    else:
        print(f"\n🎉 All base models are available!")
        print(f"You can proceed directly to building your custom models:")
        print(f"   python build_existing_models.py")
    
    # Save analysis
    with open('base_model_analysis.json', 'w') as f:
        import json
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'modelfile_analysis': modelfile_analysis,
            'base_model_counts': dict(base_model_counts),
            'available_models': available_models,
            'missing_models': missing_models,
            'suggestions': suggestions,
            'setup_plan': plan
        }, f, indent=2)
    
    print(f"\n📄 Full analysis saved to: base_model_analysis.json")
    
    return len(missing_models) == 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nAnalysis cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nAnalysis error: {e}")
        sys.exit(1)
