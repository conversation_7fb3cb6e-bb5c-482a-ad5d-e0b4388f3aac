#!/usr/bin/env python3
"""
ACTIVATE FULL AI SYSTEM
Activate the complete AI trading system with all available models
"""

import os
import sys
import time
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Any

class FullAISystemActivator:
    """Activate the complete AI trading system"""
    
    def __init__(self):
        self.available_models = []
        self.ai_teams = {}
        self.system_status = {}
        
    def discover_available_models(self) -> List[str]:
        """Discover all available AI models"""
        
        print("🔍 Discovering available AI models...")
        
        # Method 1: Check Modelfiles
        modelfiles = []
        for file in os.listdir('.'):
            if file.startswith('Modelfile.'):
                model_name = file.replace('Modelfile.', '')
                modelfiles.append(model_name)
        
        print(f"  📁 Found {len(modelfiles)} Modelfiles")
        
        # Method 2: Check model directories
        model_dirs = []
        for item in ['Fathomr1', 'kernelllm', 'mimoai', 'qwen3']:
            if os.path.exists(item) and os.path.isdir(item):
                model_dirs.append(item)
        
        print(f"  📂 Found {len(model_dirs)} model directories")
        
        # Method 3: Try ollama list (with timeout handling)
        ollama_models = []
        try:
            print("  🔍 Checking Ollama registry...")
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        ollama_models.append(model_name)
                print(f"  ✅ Ollama: {len(ollama_models)} models registered")
            else:
                print(f"  ⚠️ Ollama list failed, but service is running")
                
        except subprocess.TimeoutExpired:
            print(f"  ⚠️ Ollama list timed out, but service is running")
        except Exception as e:
            print(f"  ⚠️ Ollama check failed: {e}")
        
        # Combine all discovered models
        all_models = list(set(modelfiles + model_dirs + ollama_models))
        
        # Filter for finance/trading models
        finance_models = []
        for model in all_models:
            if any(keyword in model.lower() for keyword in 
                   ['finance', 'trading', 'noryon', 'deepseek', 'phi', 'qwen', 'gemma']):
                finance_models.append(model)
        
        self.available_models = finance_models
        
        print(f"\n✅ Discovery complete:")
        print(f"  📊 Total models found: {len(all_models)}")
        print(f"  💹 Finance/Trading models: {len(finance_models)}")
        
        return finance_models
        
    def organize_ai_teams(self) -> Dict[str, List[str]]:
        """Organize models into specialized teams"""
        
        print("\n👥 Organizing AI teams...")
        
        teams = {
            'finance_specialists': [],
            'risk_managers': [],
            'technical_analysts': [],
            'reasoning_engines': [],
            'enhanced_models': [],
            'unrestricted_models': []
        }
        
        for model in self.available_models:
            model_lower = model.lower()
            
            # Finance specialists
            if 'finance' in model_lower:
                teams['finance_specialists'].append(model)
            
            # Risk managers
            if any(keyword in model_lower for keyword in ['risk', 'cogito', 'deepseek']):
                teams['risk_managers'].append(model)
            
            # Technical analysts
            if any(keyword in model_lower for keyword in ['phi', 'gemma', 'technical']):
                teams['technical_analysts'].append(model)
            
            # Reasoning engines
            if any(keyword in model_lower for keyword in ['reasoning', 'marco', 'qwen']):
                teams['reasoning_engines'].append(model)
            
            # Enhanced models
            if 'enhanced' in model_lower:
                teams['enhanced_models'].append(model)
            
            # Unrestricted models
            if any(keyword in model_lower for keyword in ['unrestricted', 'liberated', 'maximum']):
                teams['unrestricted_models'].append(model)
        
        self.ai_teams = teams
        
        print(f"  🏦 Finance Specialists: {len(teams['finance_specialists'])}")
        print(f"  🛡️ Risk Managers: {len(teams['risk_managers'])}")
        print(f"  📈 Technical Analysts: {len(teams['technical_analysts'])}")
        print(f"  🧠 Reasoning Engines: {len(teams['reasoning_engines'])}")
        print(f"  ⚡ Enhanced Models: {len(teams['enhanced_models'])}")
        print(f"  🔓 Unrestricted Models: {len(teams['unrestricted_models'])}")
        
        return teams
        
    def test_model_availability(self, model_name: str) -> bool:
        """Test if a specific model is available and working"""
        
        try:
            # Try to run the model with a simple query
            result = subprocess.run([
                'ollama', 'run', model_name, 'Test: What is 1+1?'
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and len(result.stdout.strip()) > 0:
                return True
            else:
                return False
                
        except Exception:
            return False
            
    def activate_ai_teams(self) -> Dict[str, Any]:
        """Activate AI teams and test their functionality"""
        
        print("\n🚀 Activating AI teams...")
        
        activation_results = {}
        
        for team_name, models in self.ai_teams.items():
            print(f"\n  🔄 Activating {team_name}...")
            
            working_models = []
            failed_models = []
            
            # Test first 3 models from each team (to save time)
            test_models = models[:3] if len(models) > 3 else models
            
            for model in test_models:
                print(f"    🧪 Testing {model}...")
                
                if self.test_model_availability(model):
                    working_models.append(model)
                    print(f"      ✅ {model}: Working")
                else:
                    failed_models.append(model)
                    print(f"      ❌ {model}: Not responding")
            
            activation_results[team_name] = {
                'total_models': len(models),
                'tested_models': len(test_models),
                'working_models': working_models,
                'failed_models': failed_models,
                'success_rate': len(working_models) / len(test_models) if test_models else 0,
                'status': 'OPERATIONAL' if working_models else 'FAILED'
            }
            
            print(f"    📊 {team_name}: {len(working_models)}/{len(test_models)} working")
        
        return activation_results
        
    def run_ai_trading_demo(self) -> Dict[str, Any]:
        """Run a quick AI trading demonstration"""
        
        print("\n🎯 Running AI trading demonstration...")
        
        # Find best working model
        best_model = None
        for team_name, results in self.system_status.items():
            if results.get('working_models'):
                best_model = results['working_models'][0]
                break
        
        if not best_model:
            print("  ❌ No working models found for demo")
            return {'success': False, 'error': 'No working models'}
        
        print(f"  🤖 Using model: {best_model}")
        
        # Test trading analysis
        try:
            query = """
            Analyze AAPL stock and provide a trading recommendation.
            Consider current market conditions and provide:
            1. Action (BUY/SELL/HOLD)
            2. Confidence level (0-1)
            3. Brief reasoning
            
            Keep response concise.
            """
            
            print(f"  📊 Requesting AAPL analysis...")
            
            result = subprocess.run([
                'ollama', 'run', best_model, query
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and len(result.stdout.strip()) > 20:
                response = result.stdout.strip()
                
                print(f"  ✅ AI Analysis received ({len(response)} chars)")
                print(f"  📝 Preview: {response[:100]}...")
                
                return {
                    'success': True,
                    'model_used': best_model,
                    'response_length': len(response),
                    'response_preview': response[:200],
                    'full_response': response
                }
            else:
                print(f"  ❌ AI analysis failed")
                return {'success': False, 'error': 'No valid response'}
                
        except Exception as e:
            print(f"  ❌ Demo failed: {e}")
            return {'success': False, 'error': str(e)}
            
    def generate_activation_report(self) -> Dict[str, Any]:
        """Generate comprehensive activation report"""
        
        total_models = len(self.available_models)
        total_teams = len(self.ai_teams)
        
        working_teams = sum(1 for results in self.system_status.values() 
                           if results.get('status') == 'OPERATIONAL')
        
        total_working_models = sum(len(results.get('working_models', [])) 
                                 for results in self.system_status.values())
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'discovery': {
                'total_models_found': total_models,
                'finance_models': len([m for m in self.available_models if 'finance' in m.lower()]),
                'enhanced_models': len([m for m in self.available_models if 'enhanced' in m.lower()]),
                'unrestricted_models': len([m for m in self.available_models if 'unrestricted' in m.lower()])
            },
            'teams': {
                'total_teams': total_teams,
                'operational_teams': working_teams,
                'team_details': self.system_status
            },
            'performance': {
                'total_working_models': total_working_models,
                'overall_success_rate': working_teams / total_teams if total_teams > 0 else 0,
                'system_status': 'FULLY_OPERATIONAL' if working_teams >= total_teams * 0.8 else 
                               'MOSTLY_OPERATIONAL' if working_teams >= total_teams * 0.5 else 'DEGRADED'
            },
            'available_models': self.available_models,
            'ai_teams': self.ai_teams
        }
        
        return report

def main():
    """Main activation function"""
    
    print("🚀 FULL AI SYSTEM ACTIVATION")
    print("=" * 50)
    print("Activating your complete AI trading system with all available models...")
    print()
    
    activator = FullAISystemActivator()
    
    # Step 1: Discover models
    models = activator.discover_available_models()
    
    if not models:
        print("❌ No AI models found!")
        return False
    
    # Step 2: Organize teams
    teams = activator.organize_ai_teams()
    
    # Step 3: Activate teams
    activator.system_status = activator.activate_ai_teams()
    
    # Step 4: Run demo
    demo_results = activator.run_ai_trading_demo()
    
    # Step 5: Generate report
    report = activator.generate_activation_report()
    
    # Save report
    report_filename = f"full_ai_activation_report_{int(time.time())}.json"
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print(f"\n" + "=" * 50)
    print(f"🎯 FULL AI SYSTEM ACTIVATION COMPLETE")
    print(f"=" * 50)
    
    status = report['performance']['system_status']
    working_models = report['performance']['total_working_models']
    total_teams = report['teams']['total_teams']
    operational_teams = report['teams']['operational_teams']
    
    print(f"📊 System Status: {status}")
    print(f"🤖 Working Models: {working_models}")
    print(f"👥 Operational Teams: {operational_teams}/{total_teams}")
    
    if demo_results.get('success'):
        print(f"✅ AI Demo: SUCCESS")
        print(f"🧠 Model Used: {demo_results.get('model_used')}")
    else:
        print(f"⚠️ AI Demo: {demo_results.get('error', 'Failed')}")
    
    print(f"\n📄 Full report saved to: {report_filename}")
    
    if status in ['FULLY_OPERATIONAL', 'MOSTLY_OPERATIONAL']:
        print(f"\n🎉 YOUR AI TRADING SYSTEM IS READY!")
        print(f"Start trading with: python start_ai_paper_trading.py")
        return True
    else:
        print(f"\n⚠️ System needs attention. Check the report for details.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nActivation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nActivation error: {e}")
        sys.exit(1)
