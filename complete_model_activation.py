#!/usr/bin/env python3
"""
COMPLETE MODEL ACTIVATION
Pull base models and build your 133 custom AI models for trading
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def pull_essential_base_models():
    """Pull the essential base models needed for your Modelfiles"""
    
    print("📥 PULLING ESSENTIAL BASE MODELS")
    print("=" * 40)
    print("Pulling the most important base models for your 133 Modelfiles...")
    print()
    
    # Essential models based on your analysis
    essential_models = [
        'qwen2.5:7b',      # For qwen3 models
        'phi3:mini',       # For phi4 models  
        'gemma2:9b',       # For gemma3 models
        'deepseek-chat',   # For deepseek-r1 models
        'qwen2.5:14b',     # Larger qwen variant
        'phi3:14b',        # Larger phi variant
        'gemma2:27b',      # Larger gemma variant
        'deepseek-coder'   # Alternative deepseek
    ]
    
    successful_pulls = []
    failed_pulls = []
    
    for i, model in enumerate(essential_models):
        print(f"[{i+1}/{len(essential_models)}] Pulling {model}...")
        
        try:
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'pull', model
            ], capture_output=True, text=True, timeout=600)  # 10 minute timeout per model
            
            pull_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"  ✅ {model}: Success ({pull_time:.1f}s)")
                successful_pulls.append(model)
            else:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                print(f"  ❌ {model}: Failed - {error_msg[:100]}")
                failed_pulls.append(model)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {model}: Timed out (>10 minutes)")
            failed_pulls.append(model)
        except Exception as e:
            print(f"  ❌ {model}: Error - {e}")
            failed_pulls.append(model)
    
    print(f"\n📊 Base Model Results:")
    print(f"  ✅ Successful: {len(successful_pulls)}")
    print(f"  ❌ Failed: {len(failed_pulls)}")
    
    return successful_pulls, failed_pulls

def build_priority_custom_models(available_base_models):
    """Build priority custom models from your Modelfiles"""
    
    print(f"\n🔨 BUILDING PRIORITY CUSTOM MODELS")
    print("=" * 40)
    print("Building your most important finance and trading models...")
    print()
    
    # Find Modelfiles that can be built with available base models
    buildable_models = []
    
    for file in os.listdir('.'):
        if file.startswith('Modelfile.'):
            model_name = file.replace('Modelfile.', '')
            
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check if this Modelfile can be built with available models
                from_line = None
                for line in content.split('\n'):
                    if line.strip().upper().startswith('FROM '):
                        from_line = line.strip()
                        break
                
                if from_line:
                    base_model = from_line.split()[1]
                    
                    # Check if we can substitute the base model
                    can_build = False
                    substitute_model = None
                    
                    for available in available_base_models:
                        # Direct match
                        if base_model == available:
                            can_build = True
                            substitute_model = available
                            break
                        
                        # Partial match (e.g., qwen3 -> qwen2.5)
                        base_name = base_model.split(':')[0].lower()
                        avail_name = available.split(':')[0].lower()
                        
                        if ('qwen' in base_name and 'qwen' in avail_name) or \
                           ('phi' in base_name and 'phi' in avail_name) or \
                           ('gemma' in base_name and 'gemma' in avail_name) or \
                           ('deepseek' in base_name and 'deepseek' in avail_name):
                            can_build = True
                            substitute_model = available
                            break
                    
                    if can_build:
                        priority = 0
                        # Higher priority for finance models
                        if 'finance' in model_name.lower():
                            priority += 100
                        # Higher priority for enhanced models
                        if 'enhanced' in model_name.lower():
                            priority += 50
                        # Higher priority for noryon models
                        if 'noryon' in model_name.lower():
                            priority += 25
                        
                        buildable_models.append({
                            'model_name': model_name,
                            'filename': file,
                            'original_base': base_model,
                            'substitute_base': substitute_model,
                            'priority': priority
                        })
                        
            except Exception as e:
                print(f"  ⚠️ Error analyzing {file}: {e}")
    
    # Sort by priority
    buildable_models.sort(key=lambda x: x['priority'], reverse=True)
    
    print(f"  🎯 Found {len(buildable_models)} buildable models")
    
    # Build top priority models
    max_builds = 10  # Build top 10 models
    build_list = buildable_models[:max_builds]
    
    successful_builds = []
    failed_builds = []
    
    for i, model_info in enumerate(build_list):
        model_name = model_info['model_name']
        filename = model_info['filename']
        substitute_base = model_info['substitute_base']
        
        print(f"\n[{i+1}/{len(build_list)}] Building {model_name}...")
        print(f"  📁 From: {filename}")
        print(f"  🔄 Using base: {substitute_base}")
        
        try:
            # Create a temporary Modelfile with the substitute base model
            temp_filename = f"temp_{filename}"
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace the FROM line
            lines = content.split('\n')
            for j, line in enumerate(lines):
                if line.strip().upper().startswith('FROM '):
                    lines[j] = f"FROM {substitute_base}"
                    break
            
            modified_content = '\n'.join(lines)
            
            with open(temp_filename, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            # Build the model
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'create', model_name, '-f', temp_filename
            ], capture_output=True, text=True, timeout=300)  # 5 minute timeout
            
            build_time = time.time() - start_time
            
            # Clean up temp file
            os.remove(temp_filename)
            
            if result.returncode == 0:
                print(f"  ✅ {model_name}: Built successfully ({build_time:.1f}s)")
                successful_builds.append(model_name)
                
                # Quick test
                print(f"  🧪 Testing {model_name}...")
                test_result = subprocess.run([
                    'ollama', 'run', model_name, 'Test: What is 1+1?'
                ], capture_output=True, text=True, timeout=20)
                
                if test_result.returncode == 0 and len(test_result.stdout.strip()) > 0:
                    print(f"    ✅ Test passed")
                else:
                    print(f"    ⚠️ Test failed")
                    
            else:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                print(f"  ❌ {model_name}: Build failed - {error_msg[:100]}")
                failed_builds.append(model_name)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {model_name}: Build timed out")
            failed_builds.append(model_name)
        except Exception as e:
            print(f"  ❌ {model_name}: Error - {e}")
            failed_builds.append(model_name)
    
    return successful_builds, failed_builds

def test_ai_trading_capability(working_models):
    """Test AI trading capability with working models"""
    
    if not working_models:
        print("\n❌ No working models to test")
        return False
    
    print(f"\n🎯 TESTING AI TRADING CAPABILITY")
    print("=" * 40)
    
    test_model = working_models[0]
    print(f"Testing with: {test_model}")
    
    trading_query = """
    Analyze AAPL stock for trading. Provide:
    1. Action: BUY, SELL, or HOLD
    2. Confidence: 0-100%
    3. Brief reasoning (2-3 sentences)
    
    Keep response concise and structured.
    """
    
    try:
        print(f"  🔍 Requesting AAPL analysis...")
        
        result = subprocess.run([
            'ollama', 'run', test_model, trading_query
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and len(result.stdout.strip()) > 20:
            response = result.stdout.strip()
            print(f"  ✅ AI Analysis successful!")
            print(f"  📝 Response length: {len(response)} characters")
            print(f"  📄 Preview: {response[:150]}...")
            
            return True
        else:
            print(f"  ❌ AI Analysis failed")
            return False
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def main():
    """Main activation function"""
    
    print("🚀 COMPLETE MODEL ACTIVATION")
    print("=" * 60)
    print("Activating your complete AI trading system:")
    print("  • Pull essential base models")
    print("  • Build priority custom models")
    print("  • Test AI trading capability")
    print()
    
    # Step 1: Pull base models
    successful_pulls, failed_pulls = pull_essential_base_models()
    
    if not successful_pulls:
        print("❌ No base models could be pulled. Check internet connection.")
        return False
    
    # Step 2: Build custom models
    successful_builds, failed_builds = build_priority_custom_models(successful_pulls)
    
    if not successful_builds:
        print("❌ No custom models could be built.")
        return False
    
    # Step 3: Test trading capability
    trading_works = test_ai_trading_capability(successful_builds)
    
    # Final summary
    print(f"\n" + "=" * 60)
    print(f"🎉 COMPLETE MODEL ACTIVATION RESULTS")
    print(f"=" * 60)
    
    print(f"📥 Base Models:")
    print(f"  ✅ Pulled: {len(successful_pulls)}")
    print(f"  ❌ Failed: {len(failed_pulls)}")
    
    print(f"\n🔨 Custom Models:")
    print(f"  ✅ Built: {len(successful_builds)}")
    print(f"  ❌ Failed: {len(failed_builds)}")
    
    print(f"\n🎯 AI Trading Test: {'✅ PASSED' if trading_works else '❌ FAILED'}")
    
    if successful_builds:
        print(f"\n🤖 Working AI Models:")
        for model in successful_builds:
            print(f"  • {model}")
        
        print(f"\n🚀 YOUR AI TRADING SYSTEM IS NOW READY!")
        print(f"Start trading with:")
        print(f"  python start_ai_paper_trading.py")
        print(f"  python simplified_paper_trading.py")
        
        # Save results
        with open('model_activation_results.json', 'w') as f:
            import json
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'base_models_pulled': successful_pulls,
                'custom_models_built': successful_builds,
                'trading_test_passed': trading_works,
                'total_working_models': len(successful_builds)
            }, f, indent=2)
        
        print(f"\n📄 Results saved to: model_activation_results.json")
        
        return True
    else:
        print(f"\n⚠️ No models are working. System needs attention.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nActivation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nActivation error: {e}")
        sys.exit(1)
